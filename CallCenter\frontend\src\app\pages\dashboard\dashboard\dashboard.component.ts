import { Component } from '@angular/core';
import { CallQualitySummaryComponent } from "../call-quality-summary/call-quality-summary.component";
import { TopAgentsListComponent } from "../top-agents-list/top-agents-list.component";
import { RecentAlertsComponent } from "../recent-alerts/recent-alerts.component";
import { QualityTrendChartComponent } from "../quality-trend-chart/quality-trend-chart.component";

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [CallQualitySummaryComponent, TopAgentsListComponent, RecentAlertsComponent, QualityTrendChartComponent],
})
export class DashboardComponent {}
